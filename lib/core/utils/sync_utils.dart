import 'dart:convert';
import 'dart:io';

import 'package:realm/realm.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_sign_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_signature_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/sync_pic_request_entity.dart'
    as sync_pic;
import 'package:storetrack_app/features/home/<USER>/entities/sync_sign_request_entity.dart'
    as sync_sign;
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// Utility class for sync-related operations
///
/// This class provides helper methods for creating API request entities
/// and processing data transformations needed for the four new sync endpoints:
///
/// 1. `/send_task_pic_v4_11` - Upload individual photos
/// 2. `/sync_pic_info_mpt` - Sync photo metadata after all uploads
/// 3. `/send_task_sig_v4_11` - Upload individual signatures
/// 4. `/sync_sig_info` - Sync signature metadata after all uploads
///
/// Usage example:
/// ```dart
/// // 1. Upload individual photos first
/// final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
/// for (final photoData in photosToUpload) {
///   final request = await SyncUtils.createUploadPhotoRequest(
///     photo: photoData['photo'],
///     taskId: photoData['taskId'],
///     folderId: photoData['folderId'],
///   );
///   final result = await homeRepository.uploadPhoto(request);
/// }
///
/// // 2. Then sync photo metadata
/// final syncRequest = await SyncUtils.createSyncPicInfoRequest(tasks: tasks);
/// final syncResult = await homeRepository.syncPhotoInfo(syncRequest);
/// ```
class SyncUtils {
  static const String deviceUid = "8b7a6774c878a206";

  /// Encode file to base64 string
  static Future<String> encodeFileToBase64(String filePath) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    final base64String = base64Encode(bytes);
    return base64String;
  }

  /// Create upload photo request entity from photo data
  static Future<UploadPhotoRequestEntity> createUploadPhotoRequest({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadPhotoRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.photoId = photo.photoId?.toInt();
    request.photoDate = null;
    request.photoCaption = photo.caption;
    request.cannotUploadMandatory = photo.cannotUploadMandatory;
    request.formId = photo.formId?.toInt();
    request.questionId = photo.questionId?.toInt();
    request.measurementId = photo.measurementId?.toInt();
    request.questionpartId = photo.questionpartId?.toInt();
    request.questionPartMultiId = photo.questionPartMultiId;
    request.measurementPhototypeId = photo.measurementPhototypeId?.toInt();
    request.deviceuid = deviceUid;

    if (photo.cannotUploadMandatory == true) {
      request.pictureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use photoUrl
      final imagePath = photo.localPath ?? photo.photoUrl ?? "";
      request.pictureBlob = await encodeFileToBase64(imagePath);
    }

    return request;
  }

  /// Create upload signature request entity from signature data
  static Future<UploadSignRequestEntity> createUploadSignatureRequest({
    required Signature signature,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadSignRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.signatureId = signature.signatureId?.toInt();
    request.signedBy = signature.signedBy;
    request.cannotUploadMandatory = signature.cannotUploadMandatory;
    request.formId = signature.formId?.toInt();
    request.questionId = signature.questionId?.toInt();
    request.deviceuid = deviceUid;

    if (signature.cannotUploadMandatory == true) {
      request.signatureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use signatureUrl
      final signaturePath = signature.localPath ?? signature.signatureUrl ?? "";
      request.signatureBlob = await encodeFileToBase64(signaturePath);
    }

    return request;
  }

  /// Create sync pic info request entity for photo metadata sync
  static Future<sync_pic.SyncPicInfoRequestEntity> createSyncPicInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncPicRequest = sync_pic.SyncPicInfoRequestEntity();
    final List<sync_pic.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_pic.PhotoFolder> photoFolderList = [];

      for (var photoFolder in task.photoFolder ?? []) {
        final deletePhotosIds = <int>[];

        for (var photo in photoFolder.photos ?? []) {
          if (photo.userDeletedPhoto == true) {
            deletePhotosIds.add(photo.photoId?.toInt() ?? 0);
          }
        }

        if (deletePhotosIds.isNotEmpty) {
          photoFolderList.add(sync_pic.PhotoFolder(
            folderId: photoFolder.folderId?.toInt(),
            deletePhotosIds: deletePhotosIds.join(","),
          ));
        }
      }

      if (photoFolderList.isNotEmpty) {
        tasksSync.add(sync_pic.Task(
          taskId: task.taskId?.toInt(),
          uploadPhotosSuccess: true,
          modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
          photoFolder: photoFolderList,
        ));
      }
    }

    syncPicRequest.tasks = tasksSync;
    syncPicRequest.token = await dataManager.getAuthToken();
    syncPicRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncPicRequest.deviceuid = deviceUid;

    return syncPicRequest;
  }

  /// Create sync signature info request entity for signature metadata sync
  static Future<sync_sign.SyncSignInfoRequestEntity>
      createSyncSignatureInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncSignRequest = sync_sign.SyncSignInfoRequestEntity();
    final List<sync_sign.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_sign.SignatureFolder> signatureFolderList = [];

      for (var signatureFolder in task.signatureFolder ?? []) {
        final deleteSignatureIds = <int>[];

        for (var signature in signatureFolder.signatures ?? []) {
          if (signature.userDeletedSignature == true) {
            deleteSignatureIds.add(signature.signatureId?.toInt() ?? 0);
          }
        }

        if (deleteSignatureIds.isNotEmpty) {
          signatureFolderList.add(sync_sign.SignatureFolder(
            folderId: signatureFolder.folderId?.toInt(),
            deleteSignaturesIds: deleteSignatureIds.join(","),
          ));
        }
      }

      if (signatureFolderList.isNotEmpty) {
        tasksSync.add(sync_sign.Task(
          taskId: task.taskId?.toInt(),
          uploadSignatureSuccess: true,
          modifiedTimeStampSignatures: task.modifiedTimeStampSignatures,
          signatureFolder: signatureFolderList,
        ));
      }
    }

    syncSignRequest.tasks = tasksSync;
    syncSignRequest.token = await dataManager.getAuthToken();
    syncSignRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncSignRequest.deviceuid = deviceUid;

    return syncSignRequest;
  }

  /// Get all photos that need to be uploaded from tasks
  static List<Map<String, dynamic>> getPhotosToUpload(List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> photosToUpload = [];

    for (var task in tasks) {
      for (var photoFolder in task.photoFolder ?? []) {
        for (var photo in photoFolder.photos ?? []) {
          // Only include photos that haven't been deleted and have content
          if (photo.userDeletedPhoto != true &&
              (photo.photoUrl != null || photo.localPath != null)) {
            photosToUpload.add({
              'photo': photo,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': photoFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return photosToUpload;
  }

  /// Get all signatures that need to be uploaded from tasks
  static List<Map<String, dynamic>> getSignaturesToUpload(
      List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> signaturesToUpload = [];

    for (var task in tasks) {
      for (var signatureFolder in task.signatureFolder ?? []) {
        for (var signature in signatureFolder.signatures ?? []) {
          // Only include signatures that haven't been deleted and have content
          if (signature.userDeletedSignature != true &&
              (signature.signatureUrl != null || signature.localPath != null)) {
            signaturesToUpload.add({
              'signature': signature,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': signatureFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return signaturesToUpload;
  }

  /// Check if a given `photoId` already exists inside any of the photo folders
  /// of the provided [task].
  ///
  /// The API that handles `/api/send_task_pic_v4_11` returns the definitive
  /// `photoId` generated by the server.  When the same image is uploaded twice
  /// (user selects the same image again, or device retries a previous upload),
  /// the backend may respond with an **existing** `photoId` instead of a newly
  /// created one.  This helper makes it easy to detect that situation so that
  /// callers can decide to ignore the duplicate or clean up the temporary
  /// local record.
  ///
  /// Returns `true` if any photo inside `task.photoFolder` has `photoId` equal
  /// to [serverPhotoId]; otherwise returns `false`.
  static bool isDuplicatePhoto({
    required TaskDetail task,
    required int serverPhotoId,
  }) {
    if (serverPhotoId == 0) return false;

    for (final photoFolder in task.photoFolder ?? []) {
      for (final photo in photoFolder.photos ?? []) {
        if ((photo.photoId ?? 0).toInt() == serverPhotoId) {
          return true;
        }
      }
    }
    return false;
  }

  /// Handles photo upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from photo upload API and:
  /// 1. Checks if the uploaded photo is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate photo record from database
  /// 3. If not duplicate: Updates the local photo record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the photo upload API
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the uploaded photo (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handlePhotoUploadResponse({
    required UploadPhotoResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverPhoto = uploadResponse.data;
      if (serverPhoto == null) {
        logger('Invalid server photo response: data is null');
        return false;
      }

      final serverPhotoId = serverPhoto.photoId?.toInt() ?? 0;

      if (serverPhotoId == 0) {
        logger('Invalid server photo ID received');
        return false;
      }

      // Check for duplicate photo using PhotoUtils
      final isDuplicate = await PhotoUtils.photoExists(
        taskId: taskId,
        photoId: serverPhotoId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate photo record
        logger(
            'Duplicate photo detected with ID: $serverPhotoId, deleting local record');
        return await PhotoUtils.deletePhotoRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update photo record and delete local file
        logger('Updating photo record with server data for ID: $serverPhotoId');
        return await PhotoUtils.updatePhotoWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          photoId: serverPhotoId,
          photoUrl: serverPhoto.photoUrl,
          caption: serverPhoto.photoCaption,
          cannotUploadMandatory: serverPhoto.cannotUploadMandatory,
          modifiedTimeStamp: serverPhoto.modifiedTimeStampPhoto,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling photo upload response: $e');
      return false;
    }
  }

  /// Handles signature upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from signature upload API and:
  /// 1. Checks if the uploaded signature is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate signature record from database
  /// 3. If not duplicate: Updates the local signature record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the signature upload API
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the uploaded signature (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handleSignatureUploadResponse({
    required UploadSignatureResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverSignature = uploadResponse.data;
      if (serverSignature == null) {
        logger('Invalid server signature response: data is null');
        return false;
      }

      final serverSignatureId = serverSignature.signatureId?.toInt() ?? 0;

      if (serverSignatureId == 0) {
        logger('Invalid server signature ID received');
        return false;
      }

      // Check for duplicate signature using SignatureUtils
      final isDuplicate = await SignatureUtils.signatureExists(
        taskId: taskId,
        signatureId: serverSignatureId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate signature record
        logger(
            'Duplicate signature detected with ID: $serverSignatureId, deleting local record');
        return await SignatureUtils.deleteSignatureRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update signature record and delete local file
        logger(
            'Updating signature record with server data for ID: $serverSignatureId');
        return await SignatureUtils.updateSignatureWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          signatureId: serverSignatureId,
          signatureUrl: serverSignature.signatureUrl,
          signedBy: serverSignature.signedBy,
          cannotUploadMandatory: serverSignature.cannotUploadMandatory,
          modifiedTimeStamp: serverSignature.modifiedTimeStampSignature,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling signature upload response: $e');
      return false;
    }
  }

  /// Create submit report request entity from task data
  ///
  /// This method prepares the request body for the submit report API,
  /// taking inspiration from the submitReportData() method in syncing.dart.
  /// It structures the request with all necessary task data including forms,
  /// question answers, and followup tasks.
  ///
  /// Parameters:
  /// - [task]: The task detail containing all data to be submitted
  ///
  /// Returns a properly structured SubmitReportRequestEntity
  static Future<submit_report.SubmitReportRequestEntity>
      createSubmitReportRequest({
    required TaskDetail task,
  }) async {
    final dataManager = sl<DataManager>();
    const String actualDeviceUid = "8b7a6774c878a206";
    const String actualAppVersion = "9.9.9";

    final request = submit_report.SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = task.taskId.toString();
    request.comment = task.comment;
    request.minutes = task.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = task.claimableKms?.toInt();
    request.pages = task.pages?.toInt();
    request.taskStatus = task.taskStatus;
    request.submissionState = task.submissionState?.toInt();
    request.taskCommencementTimeStamp = task.taskCommencementTimeStamp;
    request.taskStoppedTimeStamp = task.taskStoppedTimeStamp;
    request.scheduledTimeStamp = task.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = task.taskLatitude?.toInt();
    request.startTaskLongitude = task.taskLongitude?.toInt();
    request.taskLatitude = task.latitude?.toInt();
    request.taskLongitude = task.longitude?.toInt();
    request.budgetCalculated = 0;

    // Initialize lists
    request.forms = [];
    request.followupTasks = [];
    request.resumePauseItems = [];

    // Process forms and question answers
    for (var form in task.forms ?? []) {
      var formPost = Form();
      formPost.formId = form.formId;
      formPost.questionAnswers = [];

      for (var questionAnswer in form.questionAnswers ?? []) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswerPost = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          formPost.questionAnswers?.add(questionAnswerPost);
        }
      }

      if (formPost.questionAnswers?.isNotEmpty == true) {
        request.forms?.add(formPost);
      }
    }

    // Process followup tasks
    for (var followupTask in task.followupTasks ?? []) {
      var followupTaskPost = submit_report.FollowupTask();
      followupTaskPost.taskId = task.taskId.toString();
      followupTaskPost.visitDate = followupTask.selectedVisitDate;
      followupTaskPost.followupTypeId = 0;
      followupTaskPost.followupItemId = 0;
      followupTaskPost.budget = followupTask.selectedBudget?.toInt();
      followupTaskPost.scheduleNote = followupTask.selectedScheduleNote;
      followupTaskPost.followupNumber = followupTask.followupNumber?.toInt();
      request.followupTasks?.add(followupTaskPost);
    }

    return request;
  }

  /// Get all tasks that need to be submitted (have sync pending status)
  ///
  /// Returns a list of TaskDetail entities that are marked for sync
  static List<TaskDetail> getTasksToSubmit() {
    final realm = RealmDatabase.instance.realm;
    final taskModels = realm.all<TaskDetailModel>();

    // Convert models to entities
    final tasks =
        taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

    return tasks.where((task) => task.syncPending == true).toList();
  }

  /// Prepare tasks array for sync following the exact pattern from downloadTaskDataSimplified()
  ///
  /// This method queries all TaskDetailModel objects from the Realm database and maps
  /// each task's properties to a Task entity with the required fields for the API request.
  ///
  /// Returns a List<Task> containing all tasks with their modification timestamps
  /// and other required fields for the tasks_optimize API call.
  static Future<List<Task>> prepareTasksForSync() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskModels = realm.all<TaskDetailModel>();

      logger('📋 Preparing ${taskModels.length} tasks for sync');

      final tasks = <Task>[];
      for (final taskModel in taskModels) {
        final task = _mapTaskDetailModelToTask(taskModel);
        tasks.add(task);
      }

      logger('✅ Successfully prepared ${tasks.length} tasks for sync');
      return tasks;
    } catch (e) {
      logger('❌ Error preparing tasks for sync: $e');
      return [];
    }
  }

  /// Helper method to map individual TaskDetailModel to Task entity
  ///
  /// Maps the required fields from TaskDetailModel to Task entity following
  /// the exact pattern used in downloadTaskDataSimplified() method.
  ///
  /// [taskModel] - The TaskDetailModel from Realm database
  /// Returns a Task entity with all required fields populated
  static Task _mapTaskDetailModelToTask(TaskDetailModel taskModel) {
    return Task(
      taskId: taskModel.taskId.toString(),
      scheduledTimeStamp: taskModel.scheduledTimeStamp,
      submissionTimeStamp: taskModel.submissionTimeStamp,
      modifiedTimeStampTask: taskModel.modifiedTimeStampTask,
      modifiedTimeStampPhotos: taskModel.modifiedTimeStampPhotos,
      modifiedTimeStampSignatures: taskModel.modifiedTimeStampSignatures,
      modifiedTimeStampSignaturetypes:
          taskModel.modifiedTimeStampSignaturetypes,
      modifiedTimeStampPhototypes: taskModel.modifiedTimeStampPhototypes,
      modifiedTimeStampForms: taskModel.modifiedTimeStampForms,
      modifiedTimeStampDocuments: taskModel.modifiedTimeStampDocuments,
      phototypeIds: const [], // Empty array as per downloadTaskDataSimplified()
      forceImportFollowupTask:
          false, // False as per downloadTaskDataSimplified()
    );
  }

  // ============================================================================
  // RESPONSE PROCESSING METHODS
  // Following process_core_implementation_plan.md specifications
  // ============================================================================

  /// Process tasks response data according to process_core_implementation_plan.md
  ///
  /// This method handles the comprehensive server data synchronization including:
  /// - Task deletion
  /// - Document updates
  /// - Form updates
  /// - Photo and signature processing
  /// - Task information updates
  /// - Submission data processing
  ///
  /// [responseEntity] - TasksResponseEntity containing all update arrays from server
  static Future<void> processTasksResponse(
      TasksResponseEntity responseEntity) async {
    try {
      logger('🔄 Starting processCore data synchronization');

      final List<String> taskIDsToCalculateBudget = [];
      final Set<String> tmpTaskIDsToBeDeleted = {};

      final realm = sl<RealmDatabase>().realm;

      await realm.write(() async {
        // Process in the same order as specified in process_core_implementation_plan.md

        // (11-10) Process tasks to be deleted
        await _processDeletedTasks(
            responseEntity.deleteTaskIds, tmpTaskIDsToBeDeleted, realm);

        // (11-2) Update task documents data
        await _processDocumentUpdates(
            responseEntity.updateTasksDocuments, realm);

        // (11-3) Update tasks forms data
        await _processFormUpdates(responseEntity.updateTasksForms, realm);

        // (11-6) Update task members
        await _processTaskMemberUpdates(
            responseEntity.updateTaskMembers, realm);

        // (11-6) Update tasks information data
        await _processTaskInformationUpdates(
            responseEntity.updateTasksTasks, taskIDsToCalculateBudget, realm);

        // (11-4) Update tasks photos data
        await _processPhotoUpdates(responseEntity.updateTasksPhotos, realm);

        // (11-1) Add new tasks
        await _processNewTasks(
            responseEntity.addTasks, taskIDsToCalculateBudget, realm);

        // (11-5) Update task submission data (moved to end as per comment)
        await _processSubmissionUpdates(responseEntity.updateTasksSubmission,
            taskIDsToCalculateBudget, realm);

        // Signature processing
        await _processSignatureUpdates(
            responseEntity.updateTasksSignatures, realm);
      });

      // Post-processing budget calculations
      await _calculateBudgetsForTasks(taskIDsToCalculateBudget, realm);

      logger('✅ ProcessCore completed successfully');
    } catch (e) {
      logger('❌ Error in processCore: $e');
      rethrow;
    }
  }

  /// Process tasks to be deleted
  static Future<void> _processDeletedTasks(
    List<String>? deleteTaskIds,
    Set<String> tmpTaskIDsToBeDeleted,
    Realm realm,
  ) async {
    if (deleteTaskIds == null || deleteTaskIds.isEmpty) return;

    logger('🗑️ Processing ${deleteTaskIds.length} tasks for deletion');

    for (final taskID in deleteTaskIds) {
      if (taskID.isEmpty) continue;

      tmpTaskIDsToBeDeleted.add(taskID);

      final localTask = realm.query<TaskDetailModel>(
          'taskId == \$0', [int.tryParse(taskID)]).firstOrNull;

      if (localTask != null) {
        await _removeTaskDetailModelFromRealm(localTask, realm);
        logger('🗑️ Deleted task: $taskID');
      }
    }
  }

  /// Helper method to remove task and all related data
  static Future<void> _removeTaskDetailModelFromRealm(
      TaskDetailModel task, Realm realm) async {
    // Remove all related data first
    for (final photoFolder in task.photoFolder) {
      for (final photo in photoFolder.photos) {
        await _deleteLocalFile(photo.localPath);
      }
    }

    for (final signatureFolder in task.signatureFolder) {
      for (final signature in signatureFolder.signatures) {
        await _deleteLocalFile(signature.localPath);
      }
    }

    // Delete the task (Realm will handle cascade deletion)
    realm.delete(task);
  }

  /// Helper method to delete local files safely
  static Future<void> _deleteLocalFile(String? filePath) async {
    if (filePath == null || filePath.isEmpty) return;

    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      logger('⚠️ Error deleting local file $filePath: $e');
    }
  }

  /// Update task documents data
  static Future<void> _processDocumentUpdates(
    List<TaskDetail>? updateTasksDocuments,
    Realm realm,
  ) async {
    if (updateTasksDocuments == null || updateTasksDocuments.isEmpty) return;

    logger('📄 Processing ${updateTasksDocuments.length} document updates');

    for (final taskDetail in updateTasksDocuments) {
      try {
        final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskDetail.taskId?.toInt()]).firstOrNull;

        if (localTask != null && taskDetail.documents != null) {
          // Clear existing documents and add updated ones
          localTask.documents.clear();
          for (final docEntity in taskDetail.documents!) {
            final document = _createDocumentFromEntity(docEntity);
            localTask.documents.add(document);
          }

          if (taskDetail.modifiedTimeStampDocuments != null) {
            localTask.modifiedTimeStampDocuments =
                taskDetail.modifiedTimeStampDocuments;
          }
        }
      } catch (e) {
        logger('❌ Error processing document update: $e');
      }
    }
  }

  /// Create DocumentModel from server entity
  static DocumentModel _createDocumentFromEntity(Document docEntity) {
    final document = DocumentModel();
    document.projectId = docEntity.projectId?.toInt();
    document.documentId = docEntity.documentId?.toInt();
    document.documentTypeId = docEntity.documentTypeId?.toInt();
    document.documentName = docEntity.documentName;
    document.documentIconLink = docEntity.documentIconLink;
    document.modifiedTimeStampDocument = docEntity.modifiedTimeStampDocument;

    if (docEntity.files != null) {
      for (final fileEntity in docEntity.files!) {
        final fileElement = _createFileElementFromEntity(fileEntity);
        document.files.add(fileElement);
      }
    }

    return document;
  }

  /// Create FileElementModel from server entity
  static FileElementModel _createFileElementFromEntity(FileElement fileEntity) {
    final fileElement = FileElementModel();
    fileElement.documentId = fileEntity.documentId?.toInt();
    fileElement.projectId = fileEntity.projectId?.toInt();
    fileElement.documentFileLink = fileEntity.documentFileLink;
    fileElement.fileId = fileEntity.fileId?.toInt();
    fileElement.modifiedTimeStampFile = fileEntity.modifiedTimeStampFile;

    return fileElement;
  }

  /// Update tasks forms data
  static Future<void> _processFormUpdates(
    List<TaskDetail>? updateTasksForms,
    Realm realm,
  ) async {
    if (updateTasksForms == null || updateTasksForms.isEmpty) return;

    logger('📝 Processing ${updateTasksForms.length} form updates');

    for (final taskDetail in updateTasksForms) {
      try {
        final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskDetail.taskId?.toInt()]).firstOrNull;

        if (localTask != null) {
          // Update form counts
          localTask.ctFormsTotalCnt = taskDetail.ctFormsTotalCnt?.toInt();
          localTask.ctFormsCompletedCnt =
              taskDetail.ctFormsCompletedCnt?.toInt();

          if (taskDetail.forms != null) {
            // Update existing forms or add new ones
            for (final formEntity in taskDetail.forms!) {
              final formId = formEntity.formId?.toInt();
              if (formId == null) continue;

              // Find existing form
              final existingForm =
                  localTask.forms.where((f) => f.formId == formId).firstOrNull;

              if (existingForm != null) {
                // Update existing form structure (not answers)
                _updateFormStructure(existingForm, formEntity);
              } else {
                // Add new form
                final newForm = _createFormFromEntity(formEntity);
                localTask.forms.add(newForm);
              }
            }

            // Remove forms that no longer exist on server
            final serverFormIds = taskDetail.forms!
                .map((f) => f.formId?.toInt())
                .where((id) => id != null)
                .toSet();

            localTask.forms
                .removeWhere((form) => !serverFormIds.contains(form.formId));
          }

          // Update modification timestamp
          if (taskDetail.modifiedTimeStampForms != null) {
            localTask.modifiedTimeStampForms =
                taskDetail.modifiedTimeStampForms;
          }
        }
      } catch (e) {
        logger('❌ Error processing form update: $e');
      }
    }
  }

  /// Update form structure without overwriting answers
  static void _updateFormStructure(FormModel form, Form formEntity) {
    form.formName = formEntity.formName;
    form.briefUrl = formEntity.briefUrl;
    form.isVisionForm = formEntity.isVisionForm;
    form.visionFormUrl = formEntity.visionFormUrl;
    form.isMandatory = formEntity.isMandatory;
    form.formAllowForward = formEntity.formAllowForward;
    form.modifiedTimeStampForm = formEntity.modifiedTimeStampForm;

    // Update questions structure
    if (formEntity.questions != null) {
      form.questions.clear();
      for (final questionEntity in formEntity.questions!) {
        final question = _createQuestionFromEntity(questionEntity);
        form.questions.add(question);
      }
    }
  }

  /// Create FormModel from server entity
  static FormModel _createFormFromEntity(Form formEntity) {
    final form = FormModel();
    form.formId = formEntity.formId?.toInt();
    form.formInstanceId = formEntity.formInstanceId?.toInt();
    form.formName = formEntity.formName;
    form.briefUrl = formEntity.briefUrl;
    form.isVisionForm = formEntity.isVisionForm;
    form.visionFormUrl = formEntity.visionFormUrl;
    form.isMandatory = formEntity.isMandatory;
    form.formAllowForward = formEntity.formAllowForward;
    form.modifiedTimeStampForm = formEntity.modifiedTimeStampForm;

    // Add questions
    if (formEntity.questions != null) {
      for (final questionEntity in formEntity.questions!) {
        final question = _createQuestionFromEntity(questionEntity);
        form.questions.add(question);
      }
    }

    return form;
  }

  /// Create QuestionModel from server entity
  static QuestionModel _createQuestionFromEntity(Question questionEntity) {
    final question = QuestionModel();
    question.questionId = questionEntity.questionId?.toInt();
    question.questionOrderId = questionEntity.questionOrderId?.toInt();
    question.questionDescription = questionEntity.questionDescription;
    question.isComment = questionEntity.isComment;
    question.isCommentMandatory = questionEntity.isCommentMandatory;
    question.hasSignature = questionEntity.hasSignature;
    question.isSignatureMandatory = questionEntity.isSignatureMandatory;
    question.questionBrief = questionEntity.questionBrief;
    question.isMulti = questionEntity.isMulti;
    question.multiMeasurementId = questionEntity.multiMeasurementId?.toInt();
    question.isMultiOneAnswer = questionEntity.isMultiOneAnswer;
    question.questionTypeId = questionEntity.questionTypeId?.toInt();
    question.modifiedTimeStampQuestion =
        questionEntity.modifiedTimeStampQuestion;

    // Add question parts
    if (questionEntity.questionParts != null) {
      for (final partEntity in questionEntity.questionParts!) {
        final questionPart = _createQuestionPartFromEntity(partEntity);
        question.questionParts.add(questionPart);
      }
    }

    // Add measurements
    if (questionEntity.measurements != null) {
      for (final measurementEntity in questionEntity.measurements!) {
        final measurement = _createMeasurementFromEntity(measurementEntity);
        question.measurements.add(measurement);
      }
    }

    return question;
  }

  /// Create QuestionPartModel from server entity
  static QuestionPartModel _createQuestionPartFromEntity(
      QuestionPart partEntity) {
    final questionPart = QuestionPartModel();
    questionPart.projectid = partEntity.projectid?.toInt();
    questionPart.questionpartId = partEntity.questionpartId?.toInt();
    questionPart.questionpartDescription = partEntity.questionpartDescription;
    questionPart.price = partEntity.price;
    questionPart.companyId = partEntity.companyId?.toInt();
    questionPart.itemImage = partEntity.itemImage;
    questionPart.targeted = partEntity.targeted?.toInt();
    questionPart.modifiedTimeStampQuestionpart =
        partEntity.modifiedTimeStampQuestionpart;

    return questionPart;
  }

  /// Create MeasurementModel from server entity
  static MeasurementModel _createMeasurementFromEntity(
      Measurement measurementEntity) {
    final measurement = MeasurementModel();
    measurement.measurementId = measurementEntity.measurementId?.toInt();
    measurement.measurementTypeId =
        measurementEntity.measurementTypeId?.toInt();
    measurement.measurementDescription =
        measurementEntity.measurementDescription;
    measurement.measurementTypeName = measurementEntity.measurementTypeName;
    measurement.defaultAction = measurementEntity.defaultAction;
    measurement.measurementDefaultsResult =
        measurementEntity.measurementDefaultsResult;
    measurement.measurementOrderId =
        measurementEntity.measurementOrderId?.toInt();
    measurement.mandatoryPhototypesCount =
        measurementEntity.mandatoryPhototypesCount?.toInt();
    measurement.optionalPhototypesCount =
        measurementEntity.optionalPhototypesCount?.toInt();
    measurement.measurementImage = measurementEntity.measurementImage;
    measurement.companyid = measurementEntity.companyid?.toInt();
    measurement.validationTypeId = measurementEntity.validationTypeId?.toInt();
    measurement.required = measurementEntity.required;
    measurement.rangeValidation = measurementEntity.rangeValidation;
    measurement.expressionValidation = measurementEntity.expressionValidation;
    measurement.errorMessage = measurementEntity.errorMessage;

    // Add measurement options
    if (measurementEntity.measurementOptions != null) {
      for (final optionEntity in measurementEntity.measurementOptions!) {
        final option = _createMeasurementOptionFromEntity(optionEntity);
        measurement.measurementOptions.add(option);
      }
    }

    return measurement;
  }

  /// Create MeasurementOptionModel from server entity
  static MeasurementOptionModel _createMeasurementOptionFromEntity(
      MeasurementOption optionEntity) {
    final option = MeasurementOptionModel();
    option.measurementId = optionEntity.measurementId?.toInt();
    option.measurementOptionId = optionEntity.measurementOptionId?.toInt();
    option.measurementOptionDescription =
        optionEntity.measurementOptionDescription;
    option.budgetOffset = optionEntity.budgetOffset?.toInt();
    option.budgetOffsetType = optionEntity.budgetOffsetType?.toInt();
    option.isAnswer = optionEntity.isAnswer;
    option.modifiedTimeStampMeasurementoption =
        optionEntity.modifiedTimeStampMeasurementoption;

    return option;
  }

  /// Process task member updates
  static Future<void> _processTaskMemberUpdates(
    List<TaskMembersHelper>? updateTaskMembers,
    Realm realm,
  ) async {
    if (updateTaskMembers == null || updateTaskMembers.isEmpty) return;

    logger('👥 Processing ${updateTaskMembers.length} task member updates');

    for (final memberHelper in updateTaskMembers) {
      try {
        final taskId = memberHelper.taskId;
        if (taskId == null) continue;

        final localTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (localTask != null) {
          await _updateTaskMembers(localTask, memberHelper);
        }
      } catch (e) {
        logger('❌ Error processing task member update: $e');
      }
    }
  }

  /// Update task members with server data
  static Future<void> _updateTaskMembers(
    TaskDetailModel localTask,
    TaskMembersHelper memberHelper,
  ) async {
    if (memberHelper.taskMembers == null) return;

    // Clear existing members
    localTask.taskmembers.clear();

    // Add updated members
    for (final memberEntity in memberHelper.taskMembers!) {
      final member = _createTaskMemberFromEntity(memberEntity);
      localTask.taskmembers.add(member);
    }

    // Update modification timestamp
    localTask.modifiedTimeStampMembers = DateTime.now();
  }

  /// Create TaskmemberModel from server entity
  static TaskmemberModel _createTaskMemberFromEntity(Taskmember memberEntity) {
    final member = TaskmemberModel();
    member.fullname = memberEntity.fullname;
    member.teamLead = memberEntity.teamLead?.toInt();
    member.email = memberEntity.email;
    member.scheduleId = memberEntity.scheduleId?.toInt();
    member.taskId = memberEntity.taskId?.toInt();
    return member;
  }

  /// Process task information updates
  static Future<void> _processTaskInformationUpdates(
    List<TaskDetail>? updateTasksTasks,
    List<String> taskIDsToCalculateBudget,
    Realm realm,
  ) async {
    if (updateTasksTasks == null || updateTasksTasks.isEmpty) return;

    logger('ℹ️ Processing ${updateTasksTasks.length} task information updates');

    for (final taskDetail in updateTasksTasks) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskId.toInt()]).firstOrNull;

        if (localTask != null) {
          await _updateTaskInformation(
              localTask, taskDetail, taskIDsToCalculateBudget);
        }
      } catch (e) {
        logger('❌ Error processing task information update: $e');
      }
    }
  }

  /// Update task information with server data
  static Future<void> _updateTaskInformation(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
    List<String> taskIDsToCalculateBudget,
  ) async {
    // Update client information
    localTask.clientId = serverTaskDetail.clientId?.toInt();
    localTask.client = serverTaskDetail.client;
    localTask.clientLogoUrl = serverTaskDetail.clientLogoUrl;

    // Delete local logo file if it exists
    await _deleteLocalFile(localTask.clientLogoUrl);

    // Update store information
    localTask.storeId = serverTaskDetail.storeId?.toInt();
    localTask.storeGroupId = serverTaskDetail.storeGroupId?.toInt();
    localTask.storeGroup = serverTaskDetail.storeGroup;
    localTask.storeName = serverTaskDetail.storeName;
    localTask.location = serverTaskDetail.location;
    localTask.suburb = serverTaskDetail.suburb;
    localTask.phone = serverTaskDetail.phone;
    localTask.latitude = serverTaskDetail.latitude?.toDouble();
    localTask.longitude = serverTaskDetail.longitude?.toDouble();

    // Update task cycle information
    localTask.cycleId = serverTaskDetail.cycleId?.toInt();
    localTask.cycle = serverTaskDetail.cycle;

    // Update budget and add to calculation list
    localTask.budget = serverTaskDetail.budget?.toInt();
    localTask.originalbudget = serverTaskDetail.originalbudget?.toInt();
    final taskIdStr = localTask.taskId.toString();
    if (!taskIDsToCalculateBudget.contains(taskIdStr)) {
      taskIDsToCalculateBudget.add(taskIdStr);
    }

    // Update task status and dates
    localTask.reOpened = serverTaskDetail.reOpened;
    localTask.reOpenedReason = serverTaskDetail.reOpenedReason;
    localTask.taskStatus = serverTaskDetail.taskStatus;
    localTask.submissionState = 0; // TASK_SUBMISSION_STATE_DEFAULT

    localTask.rangeStart = serverTaskDetail.rangeStart;
    localTask.rangeEnd = serverTaskDetail.rangeEnd;
    localTask.scheduledTimeStamp = serverTaskDetail.scheduledTimeStamp;
    localTask.expires = serverTaskDetail.expires;
    localTask.connoteUrl = serverTaskDetail.connoteUrl;

    // Update task completion status
    final taskStatus = localTask.taskStatus ?? '';
    localTask.isOpen = !(taskStatus.toLowerCase() == 'successful' ||
        taskStatus.toLowerCase() == 'unsuccessful');

    localTask.syncPending = false;

    // Update POS items
    if (serverTaskDetail.posItems != null) {
      localTask.posItems.clear();
      for (final posEntity in serverTaskDetail.posItems!) {
        final posItem = _createPosItemFromEntity(posEntity);
        localTask.posItems.add(posItem);
      }
    }

    // Update followup tasks
    if (serverTaskDetail.followupTasks != null) {
      localTask.followupTasks.clear();
      for (final followupEntity in serverTaskDetail.followupTasks!) {
        final followupTask = _createFollowupTaskFromEntity(followupEntity);
        localTask.followupTasks.add(followupTask);
      }
    }

    // Update followup settings
    localTask.showFollowupIconMulti = serverTaskDetail.showFollowupIconMulti;
    localTask.followupSelectedMulti = serverTaskDetail.followupSelectedMulti;

    // Update task note and reschedule settings
    localTask.taskNote = serverTaskDetail.taskNote;
    localTask.disallowReschedule = serverTaskDetail.disallowReschedule;

    // Update modification timestamp
    localTask.modifiedTimeStampTask = serverTaskDetail.modifiedTimeStampTask;

    // Update additional settings
    localTask.posRequired = serverTaskDetail.posRequired;
    localTask.photoResPerc = serverTaskDetail.photoResPerc?.toInt();
    localTask.liveImagesOnly = serverTaskDetail.liveImagesOnly;
  }

  /// Create PosItemModel from server entity
  static PosItemModel _createPosItemFromEntity(PosItem posEntity) {
    final posItem = PosItemModel();
    posItem.itemName = posEntity.itemName;
    posItem.itemAmount = posEntity.itemAmount?.toInt();
    posItem.photoUrl = posEntity.photoUrl;
    return posItem;
  }

  /// Create FollowupTaskModel from server entity
  static FollowupTaskModel _createFollowupTaskFromEntity(
      FollowupTask followupEntity) {
    final followupTask = FollowupTaskModel();
    followupTask.showFollowupIcon = followupEntity.showFollowupIcon;
    followupTask.followupSelected = followupEntity.followupSelected;
    followupTask.selectedVisitDate = followupEntity.selectedVisitDate;
    followupTask.selectedFollowupTypeId =
        followupEntity.selectedFollowupTypeId?.toInt();
    followupTask.selectedFollowupItemId =
        followupEntity.selectedFollowupItemId?.toInt();
    followupTask.selectedBudget = followupEntity.selectedBudget?.toInt();
    followupTask.selectedFollowupType = followupEntity.selectedFollowupType;
    followupTask.selectedFollowupItem = followupEntity.selectedFollowupItem;
    followupTask.selectedScheduleNote = followupEntity.selectedScheduleNote;
    followupTask.followupNumber = followupEntity.followupNumber?.toInt();
    return followupTask;
  }

  /// Process photo updates
  static Future<void> _processPhotoUpdates(
    List<TaskDetail>? updateTasksPhotos,
    Realm realm,
  ) async {
    if (updateTasksPhotos == null || updateTasksPhotos.isEmpty) return;

    logger('📸 Processing ${updateTasksPhotos.length} photo updates');

    for (final taskDetail in updateTasksPhotos) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskId.toInt()]).firstOrNull;

        if (localTask != null) {
          await _updateTaskPhotos(localTask, taskDetail);
        }
      } catch (e) {
        logger('❌ Error processing photo update: $e');
      }
    }
  }

  /// Update task photos with server data
  static Future<void> _updateTaskPhotos(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
  ) async {
    if (serverTaskDetail.photoFolder == null) return;

    for (final folderEntity in serverTaskDetail.photoFolder!) {
      final folderId = folderEntity.folderId?.toInt();
      if (folderId == null) continue;

      final localPhotoFolder = localTask.photoFolder
          .where((f) => f.folderId == folderId)
          .firstOrNull;

      if (localPhotoFolder != null) {
        await _updatePhotoFolder(localPhotoFolder, folderEntity);
      }
    }

    // Update modification timestamp
    if (serverTaskDetail.modifiedTimeStampPhotos != null) {
      localTask.modifiedTimeStampPhotos =
          serverTaskDetail.modifiedTimeStampPhotos;
    }
  }

  /// Update photo folder with server data
  static Future<void> _updatePhotoFolder(
    PhotoFolderModel localFolder,
    PhotoFolder folderEntity,
  ) async {
    if (folderEntity.photos == null) return;

    for (final photoEntity in folderEntity.photos!) {
      final photoId = photoEntity.photoId?.toInt();
      if (photoId == null) continue;

      final localPhoto =
          localFolder.photos.where((p) => p.photoId == photoId).firstOrNull;

      if (localPhoto != null) {
        // Update existing photo if server has newer version
        await _updatePhotoIfNewer(localPhoto, photoEntity);
      } else {
        // Add new photo from server
        final newPhoto = _createPhotoFromEntity(photoEntity);
        localFolder.photos.add(newPhoto);
      }
    }

    // Update folder properties
    localFolder.folderName = folderEntity.folderName;
    localFolder.folderPictureAmount = folderEntity.folderPictureAmount?.toInt();
    localFolder.imageRec = folderEntity.imageRec;
    localFolder.attribute = folderEntity.attribute;
    localFolder.modifiedTimeStampPhototype =
        folderEntity.modifiedTimeStampPhototype;
  }

  /// Update photo if server version is newer
  static Future<void> _updatePhotoIfNewer(
    PhotoModel localPhoto,
    Photo serverPhotoEntity,
  ) async {
    final serverModified = serverPhotoEntity.modifiedTimeStampPhoto;
    final localModified = localPhoto.modifiedTimeStampPhoto;

    if (serverModified != null &&
        (localModified == null || serverModified.isAfter(localModified))) {
      // Delete old local file
      await _deleteLocalFile(localPhoto.localPath);

      // Update photo with server data
      localPhoto.photoUrl = serverPhotoEntity.photoUrl;
      localPhoto.thumbnailUrl = serverPhotoEntity.thumbnailUrl;
      localPhoto.caption = serverPhotoEntity.caption;
      localPhoto.modifiedTimeStampPhoto = serverModified;
      localPhoto.cannotUploadMandatory =
          serverPhotoEntity.cannotUploadMandatory;
      localPhoto.imageRec = serverPhotoEntity.imageRec;

      // Clear local path since we now have server version
      localPhoto.localPath = null;
      localPhoto.isEdited = false;
    }
  }

  /// Create PhotoModel from server entity
  static PhotoModel _createPhotoFromEntity(Photo photoEntity) {
    final photo = PhotoModel();
    photo.formId = photoEntity.formId?.toInt();
    photo.questionId = photoEntity.questionId?.toInt();
    photo.measurementId = photoEntity.measurementId?.toInt();
    photo.folderId = photoEntity.folderId?.toInt();
    photo.photoId = photoEntity.photoId?.toInt();
    photo.photoUrl = photoEntity.photoUrl;
    photo.thumbnailUrl = photoEntity.thumbnailUrl;
    photo.caption = photoEntity.caption;
    photo.modifiedTimeStampPhoto = photoEntity.modifiedTimeStampPhoto;
    photo.cannotUploadMandatory = photoEntity.cannotUploadMandatory;
    photo.userDeletedPhoto = photoEntity.userDeletedPhoto;
    photo.imageRec = photoEntity.imageRec;
    photo.questionpartId = photoEntity.questionpartId?.toInt();
    photo.questionPartMultiId = photoEntity.questionPartMultiId;
    photo.measurementPhototypeId = photoEntity.measurementPhototypeId?.toInt();
    photo.combineTypeId = photoEntity.combineTypeId?.toInt();
    photo.photoTagId = photoEntity.photoTagId?.toInt();
    photo.photoCombinetypeId = photoEntity.photoCombinetypeId?.toInt();
    return photo;
  }

  /// Process new tasks
  static Future<void> _processNewTasks(
    List<TaskDetail>? addTasks,
    List<String> taskIDsToCalculateBudget,
    Realm realm,
  ) async {
    if (addTasks == null || addTasks.isEmpty) return;

    logger('➕ Processing ${addTasks.length} new tasks');

    for (final taskDetail in addTasks) {
      try {
        final newTask = await _createTaskFromServerEntity(taskDetail);

        realm.add(newTask);

        // Add to budget calculation list
        final taskIdStr = newTask.taskId.toString();
        if (!taskIDsToCalculateBudget.contains(taskIdStr)) {
          taskIDsToCalculateBudget.add(taskIdStr);
        }

        logger('➕ Added new task: ${newTask.taskId}');
      } catch (e) {
        logger('❌ Error processing new task: $e');
      }
    }
  }

  /// Create TaskDetailModel from server entity
  static Future<TaskDetailModel> _createTaskFromServerEntity(
      TaskDetail taskEntity) async {
    // Use the existing TaskDetailMapper to convert entity to model
    final task = TaskDetailMapper.toModel(
      taskEntity,
      DateTime.now().millisecondsSinceEpoch, // Generate unique ID
    );

    // Set sync status for new tasks
    task.isSynced = false;
    task.syncPending = false;

    return task;
  }

  /// Process submission updates (form answers)
  static Future<void> _processSubmissionUpdates(
    List<TaskDetail>? updateTasksSubmission,
    List<String> taskIDsToCalculateBudget,
    Realm realm,
  ) async {
    if (updateTasksSubmission == null || updateTasksSubmission.isEmpty) return;

    logger('📋 Processing ${updateTasksSubmission.length} submission updates');

    for (final taskDetail in updateTasksSubmission) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskId.toInt()]).firstOrNull;

        if (localTask != null) {
          await _updateTaskSubmission(
              localTask, taskDetail, taskIDsToCalculateBudget);
        }
      } catch (e) {
        logger('❌ Error processing submission update: $e');
      }
    }
  }

  /// Update task submission data
  static Future<void> _updateTaskSubmission(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
    List<String> taskIDsToCalculateBudget,
  ) async {
    // Update form counts
    localTask.ctFormsTotalCnt = serverTaskDetail.ctFormsTotalCnt?.toInt();
    localTask.ctFormsCompletedCnt =
        serverTaskDetail.ctFormsCompletedCnt?.toInt();

    // Add to budget calculation list
    final taskIdStr = localTask.taskId.toString();
    if (!taskIDsToCalculateBudget.contains(taskIdStr)) {
      taskIDsToCalculateBudget.add(taskIdStr);
    }

    // Update task submission details
    localTask.comment = serverTaskDetail.comment;
    localTask.minutes = serverTaskDetail.minutes?.toInt();
    localTask.claimableKms = serverTaskDetail.claimableKms?.toInt();
    localTask.pages = serverTaskDetail.pages?.toInt();
    localTask.taskStatus = serverTaskDetail.taskStatus;
    localTask.submissionState = 0; // TASK_SUBMISSION_STATE_DEFAULT

    localTask.scheduledTimeStamp = serverTaskDetail.scheduledTimeStamp;
    localTask.submissionTimeStamp = serverTaskDetail.submissionTimeStamp;

    localTask.taskLatitude = serverTaskDetail.taskLatitude?.toDouble() ?? 0.0;
    localTask.taskLongitude = serverTaskDetail.taskLongitude?.toDouble() ?? 0.0;

    // Update form answers
    if (serverTaskDetail.forms != null) {
      for (final formEntity in serverTaskDetail.forms!) {
        final formId = formEntity.formId?.toInt();
        if (formId == null) continue;

        final localForm =
            localTask.forms.where((f) => f.formId == formId).firstOrNull;

        if (localForm != null) {
          await _updateFormAnswers(localForm, formEntity);
        }
      }
    }

    // Update task completion status
    final taskStatus = localTask.taskStatus ?? '';
    localTask.isOpen = !(taskStatus.toLowerCase() == 'successful' ||
        taskStatus.toLowerCase() == 'unsuccessful');

    localTask.syncPending = false;
  }

  /// Update form answers from server data
  static Future<void> _updateFormAnswers(
    FormModel localForm,
    Form formEntity,
  ) async {
    // Clear existing answers
    localForm.questionAnswers.clear();

    // Add updated answers
    if (formEntity.questionAnswers != null) {
      for (final answerEntity in formEntity.questionAnswers!) {
        final answer = _createQuestionAnswerFromEntity(answerEntity);
        localForm.questionAnswers.add(answer);
      }
    }

    // Update form completion status
    localForm.formCompleted = formEntity.formCompleted ?? false;
    // Note: formEdited property doesn't exist in FormModel, skipping

    // Rebuild multi-question parts if needed
    for (final question in localForm.questions) {
      if (question.isMulti == true) {
        await _rebuildMultiQuestionParts(localForm, question);
      }
    }
  }

  /// Create QuestionAnswerModel from server entity
  static QuestionAnswerModel _createQuestionAnswerFromEntity(
      QuestionAnswer answerEntity) {
    final answer = QuestionAnswerModel();
    answer.taskId = answerEntity.taskId?.toInt();
    answer.formId = answerEntity.formId?.toInt();
    answer.questionId = answerEntity.questionId?.toInt();
    answer.questionpartId = answerEntity.questionpartId?.toInt();
    answer.flip = answerEntity.flip;
    answer.questionPartMultiId = answerEntity.questionPartMultiId;
    answer.measurementId = answerEntity.measurementId?.toInt();
    answer.measurementTypeId = answerEntity.measurementTypeId?.toInt();
    answer.measurementOptionId = answerEntity.measurementOptionId?.toInt();
    answer.measurementOptionIds = answerEntity.measurementOptionIds;
    answer.measurementTextResult = answerEntity.measurementTextResult;
    answer.isComment = answerEntity.isComment;
    answer.commentTypeId = answerEntity.commentTypeId?.toInt();
    return answer;
  }

  /// Rebuild multi-question parts from answers
  static Future<void> _rebuildMultiQuestionParts(
    FormModel form,
    QuestionModel question,
  ) async {
    // This implements the logic from buildQuestionPartMultiFromAnswers
    // in the original Java code - rebuilding question parts based on answers

    final answers = form.questionAnswers
        .where((a) => a.questionId == question.questionId)
        .toList();

    // Clear existing question parts
    question.questionParts.clear();

    // Rebuild parts based on answers
    final Map<String, QuestionPartModel> partMap = {};

    for (final answer in answers) {
      final multiId = answer.questionPartMultiId;
      if (multiId != null && !partMap.containsKey(multiId)) {
        final questionPart = QuestionPartModel();
        questionPart.questionpartId = answer.questionpartId;
        questionPart.questionpartDescription = 'Multi Part $multiId';
        // Set other properties as needed

        partMap[multiId] = questionPart;
        question.questionParts.add(questionPart);
      }
    }
  }

  /// Process signature updates
  static Future<void> _processSignatureUpdates(
    List<TaskDetail>? updateTasksSignatures,
    Realm realm,
  ) async {
    if (updateTasksSignatures == null || updateTasksSignatures.isEmpty) return;

    logger('✍️ Processing ${updateTasksSignatures.length} signature updates');

    for (final taskDetail in updateTasksSignatures) {
      try {
        final taskId = taskDetail.taskId;
        if (taskId == null) continue;

        final localTask = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskId.toInt()]).firstOrNull;

        if (localTask != null) {
          await _updateTaskSignatures(localTask, taskDetail);
        }
      } catch (e) {
        logger('❌ Error processing signature update: $e');
      }
    }
  }

  /// Update task signatures with server data
  static Future<void> _updateTaskSignatures(
    TaskDetailModel localTask,
    TaskDetail serverTaskDetail,
  ) async {
    if (serverTaskDetail.signatureFolder == null) return;

    for (final folderEntity in serverTaskDetail.signatureFolder!) {
      final folderId = folderEntity.folderId?.toInt();
      if (folderId == null) continue;

      final localSignatureFolder = localTask.signatureFolder
          .where((f) => f.folderId == folderId)
          .firstOrNull;

      if (localSignatureFolder != null) {
        await _updateSignatureFolder(localSignatureFolder, folderEntity);
      }
    }

    // Update modification timestamp
    if (serverTaskDetail.modifiedTimeStampSignatures != null) {
      localTask.modifiedTimeStampSignatures =
          serverTaskDetail.modifiedTimeStampSignatures;
    }
  }

  /// Update signature folder with server data
  static Future<void> _updateSignatureFolder(
    SignatureFolderModel localFolder,
    SignatureFolder folderEntity,
  ) async {
    if (folderEntity.signatures == null) return;

    for (final signatureEntity in folderEntity.signatures!) {
      final signatureId = signatureEntity.signatureId?.toInt();
      if (signatureId == null) continue;

      final localSignature = localFolder.signatures
          .where((s) => s.signatureId == signatureId)
          .firstOrNull;

      if (localSignature != null) {
        // Update existing signature if server has newer version
        await _updateSignatureIfNewer(localSignature, signatureEntity);
      } else {
        // Add new signature from server
        final newSignature = _createSignatureFromEntity(signatureEntity);
        localFolder.signatures.add(newSignature);
      }
    }

    // Update folder properties
    localFolder.folderName = folderEntity.folderName;
    localFolder.attribute = folderEntity.attribute;
    localFolder.modifiedTimeStampSignaturetype =
        folderEntity.modifiedTimeStampSignaturetype;
  }

  /// Update signature if server version is newer
  static Future<void> _updateSignatureIfNewer(
    SignatureModel localSignature,
    Signature serverSignatureEntity,
  ) async {
    final serverModified = serverSignatureEntity.modifiedTimeStampSignature;
    final localModified = localSignature.modifiedTimeStampSignature;

    if (serverModified != null &&
        (localModified == null || serverModified.isAfter(localModified))) {
      // Delete old local file
      await _deleteLocalFile(localSignature.localPath);

      // Update signature with server data
      localSignature.signatureUrl = serverSignatureEntity.signatureUrl;
      localSignature.thumbnailUrl = serverSignatureEntity.thumbnailUrl;
      localSignature.signedBy = serverSignatureEntity.signedBy;
      localSignature.modifiedTimeStampSignature = serverModified;
      localSignature.cannotUploadMandatory =
          serverSignatureEntity.cannotUploadMandatory;

      // Clear local path since we now have server version
      localSignature.localPath = null;
      localSignature.isEdited = false;
    }
  }

  /// Create SignatureModel from server entity
  static SignatureModel _createSignatureFromEntity(Signature signatureEntity) {
    final signature = SignatureModel();
    signature.signatureId = signatureEntity.signatureId?.toInt();
    signature.formId = signatureEntity.formId?.toInt();
    signature.questionId = signatureEntity.questionId?.toInt();
    signature.signatureUrl = signatureEntity.signatureUrl;
    signature.thumbnailUrl = signatureEntity.thumbnailUrl;
    signature.signedBy = signatureEntity.signedBy;
    signature.modifiedTimeStampSignature =
        signatureEntity.modifiedTimeStampSignature;
    signature.userDeletedSignature = signatureEntity.userDeletedSignature;
    signature.cannotUploadMandatory = signatureEntity.cannotUploadMandatory;
    return signature;
  }

  /// Calculate budgets for specified tasks
  static Future<void> _calculateBudgetsForTasks(
      List<String> taskIds, Realm realm) async {
    if (taskIds.isEmpty) return;

    logger('💰 Calculating budgets for ${taskIds.length} tasks');

    for (final taskIdStr in taskIds) {
      try {
        final taskId = int.tryParse(taskIdStr);
        if (taskId == null) continue;

        final task =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (task != null) {
          await _calculateTaskBudget(task, realm);
        }
      } catch (e) {
        logger('❌ Error calculating budget for task $taskIdStr: $e');
      }
    }
  }

  /// Calculate budget for a specific task
  static Future<void> _calculateTaskBudget(
      TaskDetailModel task, Realm realm) async {
    // Implement budget calculation logic based on:
    // - Original budget
    // - Form completion status
    // - Answer-based budget modifications
    // - Photo requirements

    int calculatedBudget = task.originalbudget ?? task.budget ?? 0;

    // Add logic for budget calculations based on form answers
    // This would involve checking form completion, answer values, etc.

    realm.write(() {
      task.budget = calculatedBudget;
    });
  }
}
