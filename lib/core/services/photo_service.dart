import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Service for handling photo database operations
abstract class PhotoService {
  /// Save a photo to the database by adding it to TaskModel's PhotoFolder's Photos array
  Future<bool> savePhotoToTask({
    required Photo photo,
    required int taskId,
    int? folderId,
  });

  /// Get photos from TaskModel's PhotoFolder's Photos array
  Future<List<Photo>> getPhotosFromTask({
    required int taskId,
    int? folderId,
  });

  /// Delete a photo from TaskModel's PhotoFolder's Photos array
  Future<bool> deletePhotoFromTask({
    required int photoId,
    required int taskId,
    int? folderId,
  });

  /// Update photo caption in TaskModel's PhotoFolder's Photos array
  Future<bool> updatePhotoCaption({
    required int photoId,
    required String caption,
    required int taskId,
    int? folderId,
  });

  /// Get next available photo ID
  Future<int> getNextPhotoId();
}

/// Implementation of [PhotoService] using Realm database
class PhotoServiceImpl implements PhotoService {
  final RealmDatabase _realmDatabase;

  PhotoServiceImpl({required RealmDatabase realmDatabase})
      : _realmDatabase = realmDatabase;

  @override
  Future<bool> savePhotoToTask({
    required Photo photo,
    required int taskId,
    int? folderId,
  }) async {
    try {
      final realm = _realmDatabase.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      // Find or create the appropriate PhotoFolder
      PhotoFolderModel? targetFolder;
      if (folderId != null) {
        // Find specific folder by ID
        for (final folder in task.photoFolder) {
          if (folder.folderId == folderId) {
            targetFolder = folder;
            break;
          }
        }
      } else {
        // Use the first folder or create a default one
        if (task.photoFolder.isNotEmpty) {
          targetFolder = task.photoFolder.first;
        }
      }

      // Create a default folder if none exists
      if (targetFolder == null) {
        final newFolder = PhotoFolderModel(
          folderId: folderId ?? 1,
          folderName: 'Default Photos',
          attribute: true,
          imageRec: false,
          modifiedTimeStampPhototype: DateTime.now(),
        );

        realm.write(() {
          task.photoFolder.add(newFolder);
        });

        targetFolder = newFolder;
      }

      // Create PhotoModel from entity
      final photoModel = PhotoModel(
        formId: photo.formId?.toInt(),
        questionId: photo.questionId?.toInt(),
        measurementId: photo.measurementId?.toInt(),
        folderId: photo.folderId?.toInt() ?? targetFolder.folderId,
        photoId: photo.photoId?.toInt(),
        photoUrl: photo.photoUrl,
        thumbnailUrl: photo.thumbnailUrl,
        caption: photo.caption,
        modifiedTimeStampPhoto: photo.modifiedTimeStampPhoto ?? DateTime.now(),
        cannotUploadMandatory: photo.cannotUploadMandatory,
        userDeletedPhoto: photo.userDeletedPhoto ?? false,
        imageRec: photo.imageRec,
        questionpartId: photo.questionpartId?.toInt(),
        questionPartMultiId: photo.questionPartMultiId,
        measurementPhototypeId: photo.measurementPhototypeId?.toInt(),
        combineTypeId: photo.combineTypeId?.toInt(),
        photoTagId: photo.photoTagId?.toInt(),
        photoCombinetypeId: photo.photoCombinetypeId?.toInt(),
        localPath: photo.localPath,
        isEdited: photo.isEdited ?? false,
      );

      // Add photo to PhotoFolder's photos array
      realm.write(() {
        targetFolder!.photos.add(photoModel);
        // Update folder picture amount
        targetFolder.folderPictureAmount =
            (targetFolder.folderPictureAmount ?? 0) + 1;
        targetFolder.modifiedTimeStampPhototype = DateTime.now();
      });

      logger(
          'Photo saved successfully to task PhotoFolder with ID: ${photo.photoId}');

      // Update task photos timestamp
      await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());

      return true;
    } catch (e) {
      logger('Error saving photo to task: $e');
      return false;
    }
  }

  @override
  Future<List<Photo>> getPhotosFromTask({
    required int taskId,
    int? folderId,
  }) async {
    try {
      final realm = _realmDatabase.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return [];
      }

      // Collect all photos from PhotoFolders
      final List<Photo> allPhotos = [];

      if (folderId != null) {
        // Get photos from specific folder
        for (final folder in task.photoFolder) {
          if (folder.folderId == folderId) {
            final photos = folder.photos
                .where((photo) => photo.userDeletedPhoto != true)
                .map((model) => _mapToEntity(model))
                .toList();
            allPhotos.addAll(photos);
            break;
          }
        }
      } else {
        // Get photos from all folders
        for (final folder in task.photoFolder) {
          // To each photo add folderId

          final photos = folder.photos
              .where((photo) => photo.userDeletedPhoto != true)
              .map((model) => _mapToEntity(model))
              .toList();
          // Add folderId to each photo
          for (final photo in photos) {
            photo.folderId = folder.folderId;
          }
          allPhotos.addAll(photos);
        }
      }

      logger('Found ${allPhotos.length} photos from task PhotoFolders');
      return allPhotos;
    } catch (e) {
      logger('Error getting photos from task: $e');
      return [];
    }
  }

  @override
  Future<bool> deletePhotoFromTask({
    required int photoId,
    required int taskId,
    int? folderId,
  }) async {
    try {
      final realm = _realmDatabase.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      // Search for photo in PhotoFolders
      if (folderId != null) {
        // Search in specific folder
        for (final folder in task.photoFolder) {
          if (folder.folderId == folderId) {
            for (int i = folder.photos.length - 1; i >= 0; i--) {
              final photo = folder.photos[i];
              if (photo.photoId == photoId) {
                realm.write(() {
                  // Check if this is a local photo (has localPath) or server photo (has photoUrl)
                  if (photo.localPath != null && photo.localPath!.isNotEmpty) {
                    // Local photo: permanently delete the record
                    folder.photos.removeAt(i);
                    logger('Local photo permanently deleted with ID: $photoId');
                  } else {
                    // Server photo: soft delete by setting userDeletedPhoto flag
                    photo.userDeletedPhoto = true;
                    photo.modifiedTimeStampPhoto = DateTime.now();
                    logger('Server photo marked as deleted with ID: $photoId');
                  }

                  // Update folder picture amount
                  if (folder.folderPictureAmount != null &&
                      folder.folderPictureAmount! > 0) {
                    folder.folderPictureAmount =
                        folder.folderPictureAmount! - 1;
                  }
                  folder.modifiedTimeStampPhototype = DateTime.now();
                });

                // Update task photos timestamp after successful deletion
                await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());

                return true;
              }
            }
            break;
          }
        }
      } else {
        // Search in all folders
        for (final folder in task.photoFolder) {
          for (int i = folder.photos.length - 1; i >= 0; i--) {
            final photo = folder.photos[i];
            if (photo.photoId == photoId) {
              realm.write(() {
                // Check if this is a local photo (has localPath) or server photo (has photoUrl)
                if (photo.localPath != null && photo.localPath!.isNotEmpty) {
                  // Local photo: permanently delete the record
                  folder.photos.removeAt(i);
                  logger('Local photo permanently deleted with ID: $photoId');
                } else {
                  // Server photo: soft delete by setting userDeletedPhoto flag
                  photo.userDeletedPhoto = true;
                  photo.modifiedTimeStampPhoto = DateTime.now();
                  logger('Server photo marked as deleted with ID: $photoId');
                }

                // Update folder picture amount
                if (folder.folderPictureAmount != null &&
                    folder.folderPictureAmount! > 0) {
                  folder.folderPictureAmount = folder.folderPictureAmount! - 1;
                }
                folder.modifiedTimeStampPhototype = DateTime.now();
              });

              // Update task photos timestamp after successful deletion
              await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());

              return true;
            }
          }
        }
      }

      logger('Photo not found with ID: $photoId');
      return false;
    } catch (e) {
      logger('Error deleting photo from task: $e');
      return false;
    }
  }

  @override
  Future<bool> updatePhotoCaption({
    required int photoId,
    required String caption,
    required int taskId,
    int? folderId,
  }) async {
    try {
      final realm = _realmDatabase.realm;

      // Find the task
      final task =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      // Search for photo in PhotoFolders
      if (folderId != null) {
        // Search in specific folder
        for (final folder in task.photoFolder) {
          if (folder.folderId == folderId) {
            for (final photo in folder.photos) {
              if (photo.photoId == photoId) {
                realm.write(() {
                  photo.caption = caption;
                  photo.modifiedTimeStampPhoto = DateTime.now();

                  // Set isEdited flag for server photos (those with photoUrl)
                  if (photo.photoUrl != null && photo.photoUrl!.isNotEmpty) {
                    photo.isEdited = true;
                  }

                  folder.modifiedTimeStampPhototype = DateTime.now();
                });

                // Update task photos timestamp after successful caption update
                await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());

                logger('Photo caption updated for ID: $photoId');
                return true;
              }
            }
            break;
          }
        }
      } else {
        // Search in all folders
        for (final folder in task.photoFolder) {
          for (final photo in folder.photos) {
            if (photo.photoId == photoId) {
              realm.write(() {
                photo.caption = caption;
                photo.modifiedTimeStampPhoto = DateTime.now();

                // Set isEdited flag for server photos (those with photoUrl)
                if (photo.photoUrl != null && photo.photoUrl!.isNotEmpty) {
                  photo.isEdited = true;
                }

                folder.modifiedTimeStampPhototype = DateTime.now();
              });

              // Update task photos timestamp after successful caption update
              await TaskUtils.updateTaskPhotosTimestamp(taskId.toString());

              logger('Photo caption updated for ID: $photoId');
              return true;
            }
          }
        }
      }

      logger('Photo not found with ID: $photoId');
      return false;
    } catch (e) {
      logger('Error updating photo caption: $e');
      return false;
    }
  }

  @override
  Future<int> getNextPhotoId() async {
    try {
      final realm = _realmDatabase.realm;

      // Initialize newPhotoID with default value
      int newPhotoID = -1;

      // Get all tasks with photo folders
      final tasks = realm.query<TaskDetailModel>('TRUEPREDICATE');

      // Iterate through all tasks and their photo folders
      for (final task in tasks) {
        for (final photoFolderModel in task.photoFolder) {
          // Check if photoFolderModel has photos (equivalent to getPhotoFolderContents() != null)
          if (photoFolderModel.photos.isNotEmpty) {
            // Iterate through each PhotoModel in the collection
            for (final photoModel in photoFolderModel.photos) {
              try {
                // Parse the photo ID from photoModel.photoId (equivalent to getPhotoID())
                final photoIdValue = photoModel.photoId;
                if (photoIdValue != null) {
                  final existingPhotoID = photoIdValue;

                  // If the existing photo ID is less than or equal to the current newPhotoID,
                  // set newPhotoID to be one less than the existing photo ID
                  if (existingPhotoID <= newPhotoID) {
                    newPhotoID = existingPhotoID - 1;
                  }
                }
              } catch (e) {
                // Handle parsing exceptions gracefully (though photoId is already int?)
                logger('Error parsing photo ID: $e');
              }
            }
          }
        }
      }

      return newPhotoID;
    } catch (e) {
      logger('Error getting next photo ID: $e');
      return -1;
    }
  }

  /// Maps PhotoModel to Photo entity
  Photo _mapToEntity(PhotoModel model) {
    return Photo(
      formId: model.formId,
      questionId: model.questionId,
      measurementId: model.measurementId,
      folderId: model.folderId,
      photoId: model.photoId,
      photoUrl: model.photoUrl,
      thumbnailUrl: model.thumbnailUrl,
      caption: model.caption,
      modifiedTimeStampPhoto: model.modifiedTimeStampPhoto,
      cannotUploadMandatory: model.cannotUploadMandatory,
      userDeletedPhoto: model.userDeletedPhoto,
      imageRec: model.imageRec,
      questionpartId: model.questionpartId,
      questionPartMultiId: model.questionPartMultiId,
      measurementPhototypeId: model.measurementPhototypeId,
      combineTypeId: model.combineTypeId,
      photoTagId: model.photoTagId,
      photoCombinetypeId: model.photoCombinetypeId,
      localPath: model.localPath,
      isEdited: model.isEdited,
    );
  }
}
