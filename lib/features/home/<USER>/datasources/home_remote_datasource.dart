import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/network/api_client.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/features/home/<USER>/models/availability_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/history_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/induction_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/useful_links_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/update_profile_request.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_req.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../domain/entities/calendar_response_entity.dart';
import '../../domain/usecases/get_calendar_usecase.dart';
import '../../domain/entities/tasks_request_entity.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../domain/entities/submit_report_request_entity.dart';
import '../../domain/entities/submit_report_response_entity.dart';
import '../../domain/entities/previous_tasks_response_entity.dart';
import '../../domain/entities/open_count_request_entity.dart';
import '../../domain/entities/open_tasks_response_entity.dart';
import '../../domain/entities/checkin_request_entity.dart';
import '../../domain/entities/checkin_response_entity.dart';
import '../models/store_contact_model.dart';
import '../models/store_comment_model.dart';
import '../../domain/entities/vacancy_entity.dart';
import '../../domain/entities/pos_request_entity.dart';
import '../../domain/entities/pos_response_entity.dart';
import '../../domain/entities/upload_photo_request_entity.dart';
import '../../domain/entities/upload_photo_response_entity.dart';
import '../../domain/entities/upload_sign_request_entity.dart';
import '../../domain/entities/upload_signature_response_entity.dart';
import '../../domain/entities/sync_pic_request_entity.dart';
import '../../domain/entities/sync_pic_response_entity.dart';
import '../../domain/entities/sync_sign_request_entity.dart';
import '../../domain/entities/sync_signature_response_entity.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';

abstract class HomeRemoteDataSource {
  Future<Result<TasksResponseEntity>> getTasks(
    TasksRequestEntity request, {
    bool isSync = false,
  });
  Future<Result<CalendarResponseEntity>> getCalendarData(
      GetCalendarParams request);
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request);

  // New submit report batch method
  Future<Result<SubmitReportResponseEntity>> submitReportBatch(
      SubmitReportRequestEntity request);

  Future<Result<PreviousTasksResponseEntity>> getPreviousTasks(
      PreviousTasksRequestEntity request);
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasksOptimize(
      PreviousTasksRequestEntity request);
  Future<Result<OpenTaskResponseEntity>> getOpenCount(
      OpenCountRequestEntity request);
  Future<Result<UsefulLinksResponse>> getUsefulLinks({
    required String token,
    required String userId,
  });
  Future<Result<SkillsResponse>> getSkills({
    required String token,
    required String userId,
  });
  Future<Result<AvailabilityResponse>> getAvailability({
    required String token,
    required String userId,
  });
  Future<Result<InductionResponse>> getInduction({
    required String token,
    required String userId,
  });
  Future<Result<HistoryResponse>> getHistory({
    required String token,
    required String userId,
  });
  Future<Result<LeaveResponse>> getLeave({
    required String token,
    required String userId,
  });
  Future<Result<bool>> saveInduction({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> inductions,
  });
  Future<Result<bool>> saveSkills({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> skills,
  });
  Future<Result<bool>> saveAvailability({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> days,
  });
  Future<Result<bool>> deleteLeave({
    required String token,
    required String userId,
    required List<int> leaveIds,
  });
  Future<Result<bool>> addLeave({
    required String token,
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  });

  // Store Contact methods
  Future<Result<StoreContactResponse>> getStoreContacts({
    required String token,
    required String userId,
    required String storeId,
  });

  Future<Result<bool>> saveStoreContact({
    required StoreContactRequest request,
  });

  Future<Result<StoreContactTypesResponse>> getStoreContactTypes({
    required String token,
    required String userId,
    required String storeId,
  });

  // Store Comment methods
  Future<Result<StoreCommentsResponse>> getStoreComments({
    required String taskId,
    required String userId,
    required String token,
  });

  Future<Result<bool>> saveStoreComment({
    required StoreCommentRequest request,
  });

  // Auto Schedule methods
  Future<Result<String>> getAutoScheduleLink({
    required String token,
    required String userId,
    int dayOffset = 0,
  });

  // Checkin methods
  Future<Result<CheckinResponseEntity>> sendCheckin(
      CheckinRequestEntity request);

  // Profile methods
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  });

  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  });

  // Notification methods
  Future<Result<NotificationResponse>> getAlerts(NotificationReqParams request);

  // Vacancy methods
  Future<Result<List<VacancyEntity>>> getVacancies({
    required String token,
    required String userId,
  });

  Future<Result<bool>> applyToVacancy({
    required String token,
    required String userId,
    required int vacancyId,
  });

  Future<Result<bool>> referVacancy({
    required String token,
    required String userId,
    required int vacancyId,
    required String refereeEmail,
  });

  // Photo upload methods
  Future<Result<UploadPhotoResponseEntity>> uploadPhoto(
      UploadPhotoRequestEntity request);

  /// Upload photo with automatic response handling
  ///
  /// This method combines photo upload with duplicate detection and local storage optimization.
  /// It automatically processes the server response to handle duplicates and update local records.
  ///
  /// Parameters:
  /// - [request]: The photo upload request
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the photo being uploaded
  ///
  /// Returns the upload response if successful, or an error if failed
  Future<Result<UploadPhotoResponseEntity>> uploadPhotoWithHandling({
    required UploadPhotoRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  });

  Future<Result<SyncPicResponseEntity>> syncPhotoInfo(
      SyncPicInfoRequestEntity request);

  // Signature upload methods
  Future<Result<UploadSignatureResponseEntity>> uploadSignature(
      UploadSignRequestEntity request);

  /// Upload signature with automatic response handling
  ///
  /// This method combines signature upload with duplicate detection and local storage optimization.
  /// It automatically processes the server response to handle duplicates and update local records.
  ///
  /// Parameters:
  /// - [request]: The signature upload request
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the signature being uploaded
  ///
  /// Returns the upload response if successful, or an error if failed
  Future<Result<UploadSignatureResponseEntity>> uploadSignatureWithHandling({
    required UploadSignRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  });

  Future<Result<SyncSignatureResponseEntity>> syncSignatureInfo(
      SyncSignInfoRequestEntity request);

  // POS methods
  Future<Result<PosResponseEntity>> updatePos(UpdatePosRequestEntity request);

  Future<Result<bool>> referVacancyWithDetails({
    required String token,
    required String userId,
    required int vacancyId,
    required String name,
    required String email,
    required String phone,
    required String stateName,
    required String suburb,
    required String comment,
    required String preference,
  });
}

class HomeDataSourceImpl implements HomeRemoteDataSource {
  final ApiClient networkClient;

  HomeDataSourceImpl({required this.networkClient});

  @override
  Future<Result<TasksResponseEntity>> getTasks(
    TasksRequestEntity request, {
    bool isSync = false,
  }) async {
    try {
      // Use the exact same request body structure as downloadTaskDataSimplified()
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";
      const deviceUid = "8b7a6774c878a206";
      const appversion = "9.9.9";

      // Prepare tasks array following downloadTaskDataSimplified() pattern
      final tasks = await SyncUtils.prepareTasksForSync();

      // Prepare request body following downloadTaskDataSimplified() pattern
      final requestBody = {
        'device_uid': deviceUid,
        'user_id': userId,
        'appversion': appversion,
        'tasks': tasks, // Properly prepared tasks array
        'token': token,
      };

      final response = await networkClient.instance.post(
        '/tasks_optimize',
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /tasks_optimize - Success");
      logger("Response status: ${response.statusCode}");

      final tasksResponse = TasksResponseEntity.fromJson(response.data);

      // Process the response data according to process_core_implementation_plan.md
      if (isSync) {
        await SyncUtils.processTasksResponse(tasksResponse);
      }

      return Result.success(tasksResponse);
    } catch (e) {
      logger("❌ Error during getTasks: $e");
      return Result.failure(
          'Could not get task list due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<CalendarResponseEntity>> getCalendarData(
      GetCalendarParams request) async {
    try {
      final response = await networkClient.instance.get(
        '/calendar_information?token=${request.token}&user_id=${request.userId}',
      );

      final getCalendarResponse =
          CalendarResponseEntity.fromJson(response.data);

      return Result.success(getCalendarResponse);
    } catch (e) {
      logger("Error during getCalendarData: $e");
      return Result.failure(
          'Could not get calendar data due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/submit_report_v4_11',
        data: request.toJson(),
      );

      final submitReportResponse =
          SubmitReportResponseEntity.fromJson(response.data);

      return Result.success(submitReportResponse);
    } catch (e) {
      logger("Error during submitReport: $e");
      return Result.failure(
          'Could not submit report due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReportBatch(
      SubmitReportRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/submit_report_v4_11',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /submit_report_v4_11 - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final submitReportResponse =
          SubmitReportResponseEntity.fromJson(response.data);

      return Result.success(submitReportResponse);
    } catch (e) {
      logger("❌ Error during submitReportBatch: $e");
      return Result.failure(
          'Could not submit report batch due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasks(
      PreviousTasksRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/previous_tasks',
        data: request.toJson(),
      );

      final previousTasksResponse =
          PreviousTasksResponseEntity.fromJson(response.data);

      return Result.success(previousTasksResponse);
    } catch (e) {
      logger("Error during getPreviousTasks: $e");
      return Result.failure(
          'Could not get previous tasks due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasksOptimize(
      PreviousTasksRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/previous_task_optimize',
        data: request.toJson(),
      );

      final previousTasksResponse =
          PreviousTasksResponseEntity.fromJson(response.data);

      return Result.success(previousTasksResponse);
    } catch (e) {
      logger("Error during getPreviousTasksOptimize: $e");
      return Result.failure(
          'Could not get optimized previous tasks due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<OpenTaskResponseEntity>> getOpenCount(
      OpenCountRequestEntity request) async {
    try {
      final String requestURL =
          '/ot_counter2?latitude=${request.latitude}&longitude=${request.longitude}&user_id=${request.userId}&token=${request.token}';

      final response = await networkClient.instance.get(requestURL);

      final openTaskResponse = OpenTaskResponseEntity.fromJson(response.data);

      return Result.success(openTaskResponse);
    } catch (e) {
      logger("Error during getOpenCount: $e");
      return Result.failure(
          'Could not get open count data due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<UsefulLinksResponse>> getUsefulLinks({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/useful_links',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final usefulLinksResponse = UsefulLinksResponse.fromJson(response.data);
      return Result.success(usefulLinksResponse);
    } catch (e) {
      logger("Error during getUsefulLinks: $e");
      return Result.failure(
          'Could not get useful links due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<InductionResponse>> getInduction({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/induction',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final inductionResponse = InductionResponse.fromJson(response.data);
      return Result.success(inductionResponse);
    } catch (e) {
      logger("Error during getInduction: $e");
      return Result.failure(
          'Could not get induction data due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<HistoryResponse>> getHistory({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/history',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final historyResponse = HistoryResponse.fromJson(response.data);
      return Result.success(historyResponse);
    } catch (e) {
      logger("Error during getHistory: $e");
      return Result.failure(
          'Could not get history data due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<SkillsResponse>> getSkills({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/skill',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final skillsResponse = SkillsResponse.fromJson(response.data);
      return Result.success(skillsResponse);
    } catch (e) {
      logger("Error during getSkills: $e");
      return Result.failure(
          'Could not get skills due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<AvailabilityResponse>> getAvailability({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/availability_multi',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final availabilityResponse = AvailabilityResponse.fromJson(response.data);
      return Result.success(availabilityResponse);
    } catch (e) {
      logger("Error during getAvailability: $e");
      return Result.failure(
          'Could not get availability due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<LeaveResponse>> getLeave({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/leave',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final leaveResponse = LeaveResponse.fromJson(response.data);
      return Result.success(leaveResponse);
    } catch (e) {
      logger("Error during getLeave: $e");
      return Result.failure(
          'Cannot get leave data at the moment. Please try again later.');
    }
  }

  @override
  Future<Result<bool>> saveInduction({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> inductions,
  }) async {
    try {
      final response = await networkClient.instance.post(
        '/induction',
        data: {
          'token': token,
          'user_id': userId,
          'inductions': inductions,
        },
      );

      return Result.success(true);
    } catch (e) {
      logger("Error during saveInduction: $e");
      return Result.failure(
          'Could not save induction data due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> saveSkills({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> skills,
  }) async {
    try {
      final response = await networkClient.instance.post(
        '/skill',
        data: {
          'token': token,
          'user_id': userId,
          'skills': skills,
        },
      );

      return Result.success(true);
    } catch (e) {
      logger("Error during saveSkills: $e");
      return Result.failure(
          'Could not save skills due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> saveAvailability({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> days,
  }) async {
    try {
      logger("📤 POST /availability - Starting request");
      logger("Token: $token");
      logger("User ID: $userId");
      logger("Days count: ${days.length}");

      final requestBody = {
        'token': token,
        'user_id': userId,
        'days': days,
      };

      logger("Request body: ${jsonEncode(requestBody)}");

      final response = await networkClient.instance.post(
        '/availability',
        data: requestBody,
      );

      logger("✅ POST /availability - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      return Result.success(true);
    } catch (e) {
      logger("❌ Error during saveAvailability: $e");
      return Result.failure(
          'Could not save availability due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> deleteLeave({
    required String token,
    required String userId,
    required List<int> leaveIds,
  }) async {
    try {
      final response = await networkClient.instance.post(
        '/delete_leave',
        data: {
          'token': token,
          'user_id': userId,
          'leave_ids': leaveIds,
        },
      );

      // Check if the response indicates success
      return Result.success(true);
    } catch (e) {
      logger("Error during deleteLeave: $e");
      return Result.failure(
          'Could not delete leave due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> addLeave({
    required String token,
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final DateFormat dateFormat = DateFormat('yyyy-MM-dd');

      final response = await networkClient.instance.post(
        '/leave',
        data: {
          'token': token,
          'user_id': userId,
          'leave_date': dateFormat.format(startDate),
        },
      );

      // Check if the response indicates success
      return Result.success(true);
    } catch (e) {
      logger("Error during addLeave: $e");
      return Result.failure(
          'Could not add leave due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<StoreContactResponse>> getStoreContacts({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    try {
      // Use the correct API base URL from the websearch result
      final response = await networkClient.instance.get(
        '/store_contact',
        queryParameters: {
          'token': token,
          'user_id': userId,
          'store_ids': storeId,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      final storeContactResponse = StoreContactResponse.fromJson(response.data);
      return Result.success(storeContactResponse);
    } catch (e) {
      logger("Error during getStoreContacts: $e");
      return Result.failure(
          'Could not get store contacts due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> saveStoreContact({
    required StoreContactRequest request,
  }) async {
    try {
      // Use the correct API base URL for posting
      final response = await networkClient.instance.post(
        '/store_contact',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /store_contact - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      return Result.success(true);
    } catch (e) {
      logger("❌ Error during saveStoreContact: $e");
      return Result.failure(
          'Could not save store contact due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<StoreContactTypesResponse>> getStoreContactTypes({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/Store_Contact_Type',
        queryParameters: {
          'token': token,
          'user_id': userId,
          'store_id': storeId,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      final storeContactTypesResponse =
          StoreContactTypesResponse.fromJson(response.data);
      return Result.success(storeContactTypesResponse);
    } catch (e) {
      logger("Error during getStoreContactTypes: $e");
      return Result.failure(
          'Could not get store contact types due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<StoreCommentsResponse>> getStoreComments({
    required String taskId,
    required String userId,
    required String token,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/comment_store',
        queryParameters: {
          'task_id': taskId,
          'user_id': userId,
          'token': token,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      final storeCommentsResponse =
          StoreCommentsResponse.fromJson(response.data);
      return Result.success(storeCommentsResponse);
    } catch (e) {
      logger("Error during getStoreComments: $e");
      return Result.failure(
          'Could not get store comments due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> saveStoreComment({
    required StoreCommentRequest request,
  }) async {
    try {
      final response = await networkClient.instance.post(
        '/comment_store',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /comment_store - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      return Result.success(true);
    } catch (e) {
      logger("❌ Error during saveStoreComment: $e");
      return Result.failure(
          'Could not save store comment due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<String>> getAutoScheduleLink({
    required String token,
    required String userId,
    int dayOffset = 0,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/auto_scheduleLinkV2',
        queryParameters: {
          'token': token,
          'user_id': userId,
          'dayoffset': dayOffset,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ GET /auto_scheduleLinkV2 - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      // Extract the URL from the response
      // Based on the Android API, it should return a JSON with data.html_link
      if (response.data is Map<String, dynamic>) {
        final dataObject = response.data['data'];
        if (dataObject != null && dataObject is Map<String, dynamic>) {
          final link = dataObject['html_link'];
          if (link != null && link is String) {
            return Result.success(link);
          }
        }
        // Fallback to check for direct url field
        if (response.data['url'] != null) {
          return Result.success(response.data['url'] as String);
        }
      } else if (response.data is String) {
        // If the response is directly a string URL
        return Result.success(response.data as String);
      }

      logger("❌ Unexpected response format for auto schedule link");
      return Result.failure(
          'Unexpected response format from auto schedule service.');
    } catch (e) {
      logger("❌ Error during getAutoScheduleLink: $e");
      return Result.failure(
          'Could not get auto schedule link due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<CheckinResponseEntity>> sendCheckin(
      CheckinRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/checkin',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /checkin - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final checkinResponse = CheckinResponseEntity.fromJson(response.data);
      return Result.success(checkinResponse);
    } catch (e) {
      logger("❌ Error during sendCheckin: $e");
      return Result.failure(
          'Could not send checkin due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await networkClient.instance.get(
        '/profile_postal',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final profileResponse = ProfileResponse.fromJson(response.data);
      return Result.success(profileResponse);
    } catch (e) {
      logger("Error during getProfile: $e");
      return Result.failure(
        'Could not get profile data due to a server or network issue. Please try again.',
      );
    }
  }

  @override
  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  }) async {
    try {
      final response = await networkClient.instance.post(
        '/profile_postal',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      final profileResponse = ProfileResponse.fromJson(response.data);
      return Result.success(profileResponse);
    } catch (e) {
      logger("Error during updateProfile: $e");
      return Result.failure(
        'Could not update profile data due to a server or network issue. Please try again.',
      );
    }
  }

  @override
  Future<Result<NotificationResponse>> getAlerts(
      NotificationReqParams request) async {
    try {
      final response = await networkClient.instance.get(
        '/alert',
        queryParameters: {
          'user_id': request.id,
          'token': request.token,
        },
      );

      if (response.statusCode == 200) {
        final notificationResponse =
            NotificationResponse.fromJson(response.data);
        return Result.success(notificationResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data?['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      logger("Error during getAlerts: $e");
      return Result.failure(
          'An error occurred during calling getAlerts: ${e.toString()}');
    }
  }

  @override
  Future<Result<List<VacancyEntity>>> getVacancies(
      {required String token, required String userId}) {
    // This is a mocked implementation.
    // In a real scenario, you would make a network call here.
    return Future.value(Result.failure('Not implemented'));
  }

  @override
  Future<Result<bool>> applyToVacancy(
      {required String token,
      required String userId,
      required int vacancyId}) async {
    try {
      final dataManager = sl<DataManager>();
      final fcmToken = await dataManager.getFcmToken() ?? '';

      // Get user profile data for the application
      final profileResult = await getProfile(token: token, userId: userId);
      if (!profileResult.isSuccess || profileResult.data?.data == null) {
        return Result.failure('Failed to load user profile');
      }

      final userData = profileResult.data!.data!;
      final userName =
          "${userData.firstName ?? ''} ${userData.lastName ?? ''}".trim();
      final userEmail = userData.email ?? '';
      final userSuburb = userData.suburb ?? '';
      final userState = userData.state ?? '';
      final userPhone = userData.mobile ?? '';

      // Prepare request body according to API specification
      final requestBody = {
        "user_id": userId,
        "token": fcmToken,
        "rec_id": vacancyId,
        "is_referral": false,
        "person_name": userName,
        "state": userState,
        "best_contact": userPhone,
        "email": userEmail,
        "comment": "",
        "suburb": userSuburb,
        "preferred_work": "",
      };

      final response = await networkClient.instance.post(
        '/vacancy',
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data != null && data['data'] != null) {
          return Result.success(true);
        } else {
          return Result.failure('Invalid response from server');
        }
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data?['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      logger("Error during applyToVacancy: $e");
      return Result.failure(
          'Could not submit application due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> referVacancy(
      {required String token,
      required String userId,
      required int vacancyId,
      required String refereeEmail}) {
    // This is a mocked implementation.
    return Future.value(Result.failure('Not implemented'));
  }

  @override
  Future<Result<PosResponseEntity>> updatePos(
      UpdatePosRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/update_pos',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /update_pos - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final posResponse = PosResponseEntity.fromJson(response.data);
      return Result.success(posResponse);
    } catch (e) {
      logger("❌ Error during updatePos: $e");
      return Result.failure(
          'Could not update POS status due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<bool>> referVacancyWithDetails(
      {required String token,
      required String userId,
      required int vacancyId,
      required String name,
      required String email,
      required String phone,
      required String stateName,
      required String suburb,
      required String comment,
      required String preference}) async {
    print('DEBUG: HomeRemoteDataSource.referVacancyWithDetails called');
    print(
        'DEBUG: Parameters - userId: $userId, vacancyId: $vacancyId, name: $name, email: $email');

    try {
      final dataManager = sl<DataManager>();
      final fcmToken = await dataManager.getFcmToken() ?? '';
      print('DEBUG: FCM token: ${fcmToken.isNotEmpty ? "present" : "empty"}');

      // Prepare request body according to API specification
      final requestBody = {
        "user_id": userId,
        "token": fcmToken,
        "rec_id": vacancyId,
        "is_referral": true,
        "person_name": name,
        "state": stateName,
        "best_contact": phone,
        "email": email,
        "comment": comment,
        "suburb": suburb,
        "preferred_work": preference,
      };

      print('DEBUG: Request body prepared: $requestBody');

      print('DEBUG: Making API call to /vacancy');
      final response = await networkClient.instance.post(
        '/vacancy',
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      print('DEBUG: API response status: ${response.statusCode}');
      // print('DEBUG: API response data: ${response.data}');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data != null && data['data'] != null) {
          print('DEBUG: API call successful');
          return Result.success(true);
        } else {
          print('DEBUG: Invalid response from server');
          return Result.failure('Invalid response from server');
        }
      } else {
        print('DEBUG: API call failed with status: ${response.statusCode}');
        return Result.failure(
            '${response.statusCode} - ${response.data?['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      print('DEBUG: Exception during API call: $e');
      logger("Error during referVacancyWithDetails: $e");
      return Result.failure(
          'Could not submit referral due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<UploadPhotoResponseEntity>> uploadPhoto(
      UploadPhotoRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/send_task_pic_v4_11',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /send_task_pic_v4_11 - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final uploadResponse = UploadPhotoResponseEntity.fromJson(response.data);
      return Result.success(uploadResponse);
    } catch (e) {
      logger("❌ Error during uploadPhoto: $e");
      return Result.failure(
          'Could not upload photo due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<UploadPhotoResponseEntity>> uploadPhotoWithHandling({
    required UploadPhotoRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      // Step 1: Upload the photo using the existing method
      final uploadResult = await uploadPhoto(request);

      if (uploadResult.isFailure) {
        return uploadResult; // Return the failure result as-is
      }

      final uploadResponse = uploadResult.data!;

      // Step 2: Handle the response with duplicate detection and local storage optimization
      logger("🔄 Processing photo upload response for taskId: $taskId");

      final handlingSuccess = await SyncUtils.handlePhotoUploadResponse(
        uploadResponse: uploadResponse,
        taskId: taskId,
        originalLocalPath: originalLocalPath,
      );

      if (handlingSuccess) {
        logger("✅ Photo upload response handled successfully");
        return Result.success(uploadResponse);
      } else {
        logger("⚠️ Photo uploaded but response handling failed");
        // Still return success since the upload itself succeeded
        // The response handling failure is logged but doesn't fail the upload
        return Result.success(uploadResponse);
      }
    } catch (e) {
      logger("❌ Error during uploadPhotoWithHandling: $e");
      return Result.failure(
          'Could not upload photo due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<SyncPicResponseEntity>> syncPhotoInfo(
      SyncPicInfoRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/sync_pic_info_mpt',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /sync_pic_info_mpt - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final syncResponse = SyncPicResponseEntity.fromJson(response.data);
      return Result.success(syncResponse);
    } catch (e) {
      logger("❌ Error during syncPhotoInfo: $e");
      return Result.failure(
          'Could not sync photo info due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<UploadSignatureResponseEntity>> uploadSignature(
      UploadSignRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/send_task_sig_v4_11',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /send_task_sig_v4_11 - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final uploadResponse =
          UploadSignatureResponseEntity.fromJson(response.data);
      return Result.success(uploadResponse);
    } catch (e) {
      logger("❌ Error during uploadSignature: $e");
      return Result.failure(
          'Could not upload signature due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<UploadSignatureResponseEntity>> uploadSignatureWithHandling({
    required UploadSignRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      // Step 1: Upload the signature using the existing method
      final uploadResult = await uploadSignature(request);

      if (uploadResult.isFailure) {
        return uploadResult; // Return the failure result as-is
      }

      final uploadResponse = uploadResult.data!;

      // Step 2: Handle the response with duplicate detection and local storage optimization
      logger("🔄 Processing signature upload response for taskId: $taskId");

      final handlingSuccess = await SyncUtils.handleSignatureUploadResponse(
        uploadResponse: uploadResponse,
        taskId: taskId,
        originalLocalPath: originalLocalPath,
      );

      if (!handlingSuccess) {
        logger(
            "⚠️ Signature upload response handling failed for taskId: $taskId");
        // Still return success since the upload itself succeeded
        // The response handling failure is logged but doesn't fail the upload
      } else {
        logger(
            "✅ Signature upload and response handling completed for taskId: $taskId");
      }

      return uploadResult; // Return the original upload result
    } catch (e) {
      logger("❌ Error during uploadSignatureWithHandling: $e");
      return Result.failure(
          'Could not upload signature due to a server or network issue. Please try again.');
    }
  }

  @override
  Future<Result<SyncSignatureResponseEntity>> syncSignatureInfo(
      SyncSignInfoRequestEntity request) async {
    try {
      final response = await networkClient.instance.post(
        '/sync_sig_info',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      logger("✅ POST /sync_sig_info - Success");
      logger("Response status: ${response.statusCode}");
      // logger("Response data: ${response.data}");

      final syncResponse = SyncSignatureResponseEntity.fromJson(response.data);
      return Result.success(syncResponse);
    } catch (e) {
      logger("❌ Error during syncSignatureInfo: $e");
      return Result.failure(
          'Could not sync signature info due to a server or network issue. Please try again.');
    }
  }
}
